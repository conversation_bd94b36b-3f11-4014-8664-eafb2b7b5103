# 故障排除指南

## 🔍 常见问题诊断

### 1. WebSocket连接问题

#### 症状
- 页面显示"连接失败"或"连接错误"
- 右上角连接指示器显示红色
- 无法提交反馈

#### 诊断步骤
```bash
# 1. 检查本地MCP服务器
curl http://localhost:8765/health

# 2. 检查Tunnel状态
cloudflared tunnel info YOUR_TUNNEL_ID

# 3. 测试WebSocket连接
# 在浏览器控制台执行：
new WebSocket('wss://ws.your-domain.com/ws')
```

#### 解决方案
1. **本地服务器未启动**
   ```bash
   uvx mcp-feedback-enhanced@latest test --web
   ```

2. **Tunnel未运行**
   ```bash
   cd cloudflare-deployment/tunnel
   cloudflared tunnel --config config.yml run
   ```

3. **DNS配置错误**
   ```bash
   # 重新配置DNS
   cloudflared tunnel route dns YOUR_TUNNEL_ID ws.your-domain.com
   ```

### 2. 页面加载问题

#### 症状
- 页面显示404错误
- 静态资源加载失败
- 样式或脚本丢失

#### 诊断步骤
```bash
# 1. 检查Pages部署状态
wrangler pages deployment list --project-name=mcp-feedback-enhanced

# 2. 验证文件结构
ls -la cloudflare-deployment/pages/

# 3. 检查重定向规则
cat cloudflare-deployment/pages/_redirects
```

#### 解决方案
1. **重新部署Pages**
   ```bash
   cd cloudflare-deployment/pages
   wrangler pages deploy . --project-name=mcp-feedback-enhanced
   ```

2. **修复文件路径**
   确保所有资源使用相对路径：
   ```html
   <!-- 正确 -->
   <script src="./static/js/app.js"></script>
   
   <!-- 错误 -->
   <script src="/static/js/app.js"></script>
   ```

### 3. API请求失败

#### 症状
- 会话无法建立
- 数据提交失败
- 服务器响应错误

#### 诊断步骤
```bash
# 1. 测试API端点
curl https://api.your-domain.com/health

# 2. 检查Tunnel配置
cat cloudflare-deployment/tunnel/config.yml

# 3. 查看Tunnel日志
cloudflared tunnel --config config.yml run --loglevel debug
```

#### 解决方案
1. **修复API路由**
   在 `tunnel/config.yml` 中确保：
   ```yaml
   ingress:
     - hostname: api.your-domain.com
       service: http://localhost:8765
   ```

2. **检查端口配置**
   确保MCP服务器运行在正确端口：
   ```bash
   export MCP_WEB_PORT=8765
   uvx mcp-feedback-enhanced@latest test --web
   ```

### 4. 环境检测问题

#### 症状
- 页面显示错误的环境模式
- 连接到错误的端点
- 配置不生效

#### 诊断步骤
```javascript
// 在浏览器控制台执行
CloudflareConfig.debug()
```

#### 解决方案
1. **强制启用Cloudflare模式**
   ```javascript
   CloudflareConfig.setCloudflareMode(true)
   location.reload()
   ```

2. **检查URL参数**
   访问 `https://your-domain.com?cloudflare=true&debug=true`

## 🛠️ 调试工具

### 1. 浏览器调试
```javascript
// 显示配置信息
CloudflareConfig.debug()

// 测试连接
CloudflareConfig.testConnection()

// 切换模式
CloudflareConfig.setCloudflareMode(true)
```

### 2. 命令行工具
```bash
# Tunnel诊断
cloudflared tunnel info YOUR_TUNNEL_ID
cloudflared tunnel cleanup YOUR_TUNNEL_ID

# Pages诊断
wrangler pages deployment list --project-name=mcp-feedback-enhanced
wrangler pages project list

# 网络测试
curl -I https://your-domain.com
curl -I https://api.your-domain.com
```

### 3. 日志查看
```bash
# Tunnel日志
cloudflared tunnel --config config.yml run --loglevel debug

# MCP服务器日志
MCP_DEBUG=true uvx mcp-feedback-enhanced@latest test --web

# 系统日志
tail -f /var/log/cloudflared.log
```

## 🔧 性能优化

### 1. 连接优化
```yaml
# tunnel/config.yml
originRequest:
  connectTimeout: 30s
  tlsTimeout: 10s
  tcpKeepAlive: 30s
  keepAliveConnections: 10
  keepAliveTimeout: 90s
```

### 2. 缓存配置
```
# pages/_headers
/static/*
  Cache-Control: public, max-age=31536000, immutable

/*.js
  Cache-Control: public, max-age=86400

/*.css
  Cache-Control: public, max-age=86400
```

### 3. 压缩设置
在Cloudflare Dashboard中启用：
- Brotli压缩
- Gzip压缩
- 自动缩小CSS/JS/HTML

## 📊 监控检查清单

### 每日检查
- [ ] Tunnel连接状态
- [ ] Pages部署状态
- [ ] SSL证书有效性
- [ ] 访问日志异常

### 每周检查
- [ ] 性能指标
- [ ] 错误率统计
- [ ] 安全事件
- [ ] 资源使用情况

### 每月检查
- [ ] 更新Cloudflare CLI
- [ ] 更新cloudflared
- [ ] 检查域名续费
- [ ] 备份配置文件

## 🆘 紧急恢复

### 快速恢复步骤
1. **重启所有服务**
   ```bash
   # 停止服务
   pkill cloudflared
   pkill python
   
   # 重启MCP服务器
   uvx mcp-feedback-enhanced@latest test --web &
   
   # 重启Tunnel
   cloudflared tunnel --config config.yml run &
   ```

2. **回滚到本地模式**
   ```bash
   # 临时禁用Cloudflare模式
   export CLOUDFLARE_MODE=false
   uvx mcp-feedback-enhanced@latest test --web
   ```

3. **联系支持**
   - GitHub Issues: [项目链接]
   - Cloudflare Support: [支持链接]
   - 社区Discord: [Discord链接]

---

**💡 提示：保存此文档的本地副本，以便在网络问题时参考。**
