# MCP Feedback Enhanced - 简化版Cloudflare Workers部署

## 🎯 简化架构

```
用户浏览器 → Cloudflare Workers (Web UI) → ngrok → 本地MCP服务器
```

## ✨ 优势

- 🚀 **超简单部署** - 只需要一个Workers脚本
- 💰 **零成本** - 完全使用免费服务
- 🔧 **最小修改** - 本地MCP服务器无需改动
- 🌍 **全球访问** - 通过Cloudflare全球网络

## 📦 项目结构

```
cloudflare-workers-simple/
├── README.md              # 本文档
├── worker.js              # Cloudflare Workers主脚本
├── wrangler.toml          # Workers配置文件
├── deploy.sh              # 一键部署脚本
└── docs/
    ├── setup-guide.md     # 详细设置指南
    └── troubleshooting.md # 故障排除
```

## 🚀 快速开始

### 1. 准备工作
```bash
# 安装依赖
npm install -g wrangler

# 登录Cloudflare
wrangler login
```

### 2. 启动本地服务器
```bash
# 启动MCP服务器
uvx mcp-feedback-enhanced@latest test --web

# 新终端：使用ngrok暴露服务器
ngrok http 8765
# 记录ngrok提供的URL，如：https://abc123.ngrok.io
```

### 3. 配置和部署
```bash
# 克隆配置
cd cloudflare-workers-simple

# 设置本地服务器地址
wrangler secret put LOCAL_MCP_URL
# 输入：wss://abc123.ngrok.io

# 部署Workers
wrangler deploy
```

### 4. 访问应用
访问Workers提供的URL，如：`https://mcp-feedback.your-subdomain.workers.dev`

## ⚙️ 配置选项

### 环境变量
- `LOCAL_MCP_URL`: 本地MCP服务器的ngrok地址
- `DEBUG_MODE`: 启用调试模式（可选）

### 自定义域名（可选）
```bash
wrangler route add "your-domain.com/*" mcp-feedback
```

## 🔧 故障排除

### 常见问题
1. **连接失败** - 检查ngrok是否正常运行
2. **WebSocket错误** - 确认LOCAL_MCP_URL配置正确
3. **页面无法加载** - 检查Workers部署状态

### 调试命令
```bash
# 查看Workers日志
wrangler tail

# 测试本地连接
curl http://localhost:8765/health

# 测试ngrok连接
curl https://abc123.ngrok.io/health
```

## 📞 支持

- GitHub Issues: [项目链接]
- 文档: [docs/setup-guide.md](docs/setup-guide.md)

---

**🎉 享受简单的全球部署体验！**
