#!/bin/bash

# Cloudflare Tunnel设置脚本
# 用于创建和配置Cloudflare Tunnel

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v cloudflared &> /dev/null; then
        log_error "cloudflared未安装，请先安装Cloudflare Tunnel客户端"
        echo "安装命令："
        echo "  macOS: brew install cloudflared"
        echo "  Linux: wget https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-linux-amd64.deb && sudo dpkg -i cloudflared-linux-amd64.deb"
        echo "  Windows: 下载并安装 https://github.com/cloudflare/cloudflared/releases/latest/download/cloudflared-windows-amd64.exe"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 登录Cloudflare
login_cloudflare() {
    log_info "检查Cloudflare登录状态..."
    
    if ! cloudflared tunnel list &> /dev/null; then
        log_warning "需要登录Cloudflare账户"
        cloudflared tunnel login
    fi
    
    log_success "Cloudflare登录验证完成"
}

# 创建Tunnel
create_tunnel() {
    local tunnel_name="mcp-feedback-enhanced"
    
    log_info "创建Cloudflare Tunnel: $tunnel_name"
    
    # 检查Tunnel是否已存在
    if cloudflared tunnel list | grep -q "$tunnel_name"; then
        log_warning "Tunnel '$tunnel_name' 已存在"
        TUNNEL_ID=$(cloudflared tunnel list | grep "$tunnel_name" | awk '{print $1}')
    else
        # 创建新Tunnel
        TUNNEL_ID=$(cloudflared tunnel create "$tunnel_name" | grep -o '[a-f0-9-]\{36\}')
        log_success "Tunnel创建成功，ID: $TUNNEL_ID"
    fi
    
    # 更新配置文件
    sed -i.bak "s/YOUR_TUNNEL_ID/$TUNNEL_ID/g" ../tunnel/config.yml
    log_success "配置文件已更新"
}

# 配置DNS
configure_dns() {
    log_info "配置DNS记录..."
    
    read -p "请输入您的域名 (例如: example.com): " DOMAIN
    
    if [ -z "$DOMAIN" ]; then
        log_error "域名不能为空"
        exit 1
    fi
    
    # 更新配置文件中的域名
    sed -i.bak "s/your-domain.com/$DOMAIN/g" ../tunnel/config.yml
    
    # 创建DNS记录
    cloudflared tunnel route dns "$TUNNEL_ID" "$DOMAIN"
    cloudflared tunnel route dns "$TUNNEL_ID" "api.$DOMAIN"
    cloudflared tunnel route dns "$TUNNEL_ID" "ws.$DOMAIN"
    
    log_success "DNS记录配置完成"
    log_info "域名: https://$DOMAIN"
    log_info "API: https://api.$DOMAIN"
    log_info "WebSocket: https://ws.$DOMAIN"
}

# 启动Tunnel
start_tunnel() {
    log_info "启动Cloudflare Tunnel..."
    
    # 复制凭证文件
    CREDENTIALS_PATH="$HOME/.cloudflared/$TUNNEL_ID.json"
    if [ -f "$CREDENTIALS_PATH" ]; then
        cp "$CREDENTIALS_PATH" ../tunnel/credentials.json
        sed -i.bak "s|/path/to/credentials.json|$(pwd)/../tunnel/credentials.json|g" ../tunnel/config.yml
        log_success "凭证文件已配置"
    else
        log_error "找不到凭证文件: $CREDENTIALS_PATH"
        exit 1
    fi
    
    # 验证配置
    cloudflared tunnel --config ../tunnel/config.yml ingress validate
    
    log_success "Tunnel配置验证通过"
    log_info "使用以下命令启动Tunnel:"
    echo "  cloudflared tunnel --config $(pwd)/../tunnel/config.yml run"
}

# 主函数
main() {
    log_info "开始设置Cloudflare Tunnel..."
    
    check_dependencies
    login_cloudflare
    create_tunnel
    configure_dns
    start_tunnel
    
    log_success "Cloudflare Tunnel设置完成！"
    log_info "下一步："
    log_info "1. 启动本地MCP服务器: uvx mcp-feedback-enhanced@latest test --web"
    log_info "2. 在新终端启动Tunnel: cloudflared tunnel --config $(pwd)/../tunnel/config.yml run"
    log_info "3. 访问您的域名测试连接"
}

# 执行主函数
main "$@"
