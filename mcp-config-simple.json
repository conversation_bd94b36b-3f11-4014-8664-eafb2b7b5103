{"mcpServers": {"mcp-feedback-enhanced-simple": {"command": "python", "args": ["-c", "import sys; sys.path.insert(0, 'c:/Users/<USER>/Documents/code/mcp-feedback-enhanced'); from src.mcp_feedback_enhanced.server import main; main()"], "timeout": 600, "env": {"CLOUDFLARE_MODE": "true", "CLOUDFLARE_WORKERS_URL": "https://mcp-feedback-enhanced.sujianjob.workers.dev", "MCP_WEB_PORT": "8765", "MCP_DEBUG": "false"}, "autoApprove": ["interactive_feedback"]}}}