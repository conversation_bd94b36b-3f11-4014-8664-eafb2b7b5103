# MCP Feedback Enhanced - Cloudflare部署指南

## 概述

本目录包含将MCP Feedback Enhanced部署到Cloudflare的所有配置文件和脚本。

## 架构图

```
用户浏览器 → Cloudflare Pages (Web UI) → Cloudflare Tunnel → 本地MCP服务器 → AI客户端
```

## 目录结构

```
cloudflare-deployment/
├── pages/                 # Cloudflare Pages静态文件
│   ├── index.html
│   ├── static/
│   │   ├── css/
│   │   ├── js/
│   │   └── assets/
│   ├── _redirects        # 重定向规则
│   └── cloudflare-config.js
├── tunnel/               # Cloudflare Tunnel配置
│   ├── config.yml
│   └── credentials.json.example
├── scripts/              # 部署脚本
│   ├── deploy-pages.sh
│   ├── setup-tunnel.sh
│   └── build-static.py
└── docs/                 # 部署文档
    ├── setup-guide.md
    └── troubleshooting.md
```

## 快速开始

1. **设置Cloudflare账户**
   ```bash
   # 安装Cloudflare CLI
   npm install -g @cloudflare/wrangler
   
   # 登录Cloudflare
   wrangler login
   ```

2. **创建Tunnel**
   ```bash
   cd tunnel/
   ./setup-tunnel.sh
   ```

3. **部署Pages**
   ```bash
   cd scripts/
   ./deploy-pages.sh
   ```

4. **启动本地服务器**
   ```bash
   # 设置环境变量
   export CLOUDFLARE_MODE=true
   export CLOUDFLARE_TUNNEL_URL=https://your-domain.com
   
   # 启动MCP服务器
   uvx mcp-feedback-enhanced@latest test --web
   ```

## 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `CLOUDFLARE_MODE` | 启用Cloudflare模式 | `false` |
| `CLOUDFLARE_TUNNEL_URL` | Tunnel URL | - |
| `CLOUDFLARE_API_URL` | API端点URL | - |
| `MCP_WEB_PORT` | 本地服务器端口 | `8765` |

## 安全配置

- SSL/TLS自动配置
- DDoS防护启用
- 访问控制可选配置
- IP白名单支持

## 监控和日志

- Cloudflare Analytics
- Tunnel连接状态监控
- 性能指标追踪
