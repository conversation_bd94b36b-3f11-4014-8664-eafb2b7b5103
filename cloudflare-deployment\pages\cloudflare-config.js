/**
 * Cloudflare部署配置管理
 * 用于检测环境并配置API端点
 */

(function(window) {
    'use strict';

    // 环境检测
    const CloudflareConfig = {
        // 检测是否在Cloudflare Pages环境
        isCloudflarePages: function() {
            return window.location.hostname.includes('.pages.dev') || 
                   window.location.hostname !== 'localhost' && 
                   window.location.hostname !== '127.0.0.1';
        },

        // 检测是否启用Cloudflare模式
        isCloudflareMode: function() {
            return this.isCloudflarePages() || 
                   window.location.search.includes('cloudflare=true') ||
                   localStorage.getItem('cloudflare_mode') === 'true';
        },

        // 获取API基础URL
        getApiBaseUrl: function() {
            if (this.isCloudflareMode()) {
                // 云端模式：使用当前域名或配置的API域名
                const hostname = window.location.hostname;
                const protocol = window.location.protocol;
                
                // 如果有专门的API子域名
                if (hostname.startsWith('api.')) {
                    return `${protocol}//${hostname}`;
                } else {
                    // 使用主域名的API子域名
                    const apiDomain = hostname.replace(/^[^.]+\./, 'api.');
                    return `${protocol}//${apiDomain}`;
                }
            } else {
                // 本地模式：使用本地服务器
                const port = this.getLocalPort();
                return `${window.location.protocol}//${window.location.hostname}:${port}`;
            }
        },

        // 获取WebSocket URL
        getWebSocketUrl: function() {
            if (this.isCloudflareMode()) {
                // 云端模式：使用WSS协议
                const hostname = window.location.hostname;
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                
                // 如果有专门的WebSocket子域名
                if (hostname.startsWith('ws.')) {
                    return `${protocol}//${hostname}/ws`;
                } else {
                    // 使用主域名的WebSocket子域名
                    const wsDomain = hostname.replace(/^[^.]+\./, 'ws.');
                    return `${protocol}//${wsDomain}/ws`;
                }
            } else {
                // 本地模式：使用本地WebSocket
                const port = this.getLocalPort();
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                return `${protocol}//${window.location.hostname}:${port}/ws`;
            }
        },

        // 获取本地端口
        getLocalPort: function() {
            return window.location.port || '8765';
        },

        // 获取完整配置
        getConfig: function() {
            const isCloudflare = this.isCloudflareMode();
            
            return {
                mode: isCloudflare ? 'cloudflare' : 'local',
                apiBaseUrl: this.getApiBaseUrl(),
                webSocketUrl: this.getWebSocketUrl(),
                isCloudflare: isCloudflare,
                environment: {
                    hostname: window.location.hostname,
                    protocol: window.location.protocol,
                    port: window.location.port
                }
            };
        },

        // 设置Cloudflare模式
        setCloudflareMode: function(enabled) {
            localStorage.setItem('cloudflare_mode', enabled ? 'true' : 'false');
            console.log(`Cloudflare模式已${enabled ? '启用' : '禁用'}`);
        },

        // 调试信息
        debug: function() {
            const config = this.getConfig();
            console.group('🔧 Cloudflare配置信息');
            console.log('模式:', config.mode);
            console.log('API基础URL:', config.apiBaseUrl);
            console.log('WebSocket URL:', config.webSocketUrl);
            console.log('环境信息:', config.environment);
            console.log('是否Cloudflare环境:', config.isCloudflare);
            console.groupEnd();
            return config;
        },

        // 连接测试
        testConnection: async function() {
            const config = this.getConfig();
            console.log('🔍 测试连接配置...');
            
            try {
                // 测试API连接
                const apiResponse = await fetch(`${config.apiBaseUrl}/health`, {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (apiResponse.ok) {
                    console.log('✅ API连接正常');
                } else {
                    console.warn('⚠️ API连接异常:', apiResponse.status);
                }
                
                // 测试WebSocket连接
                return new Promise((resolve) => {
                    const ws = new WebSocket(config.webSocketUrl);
                    const timeout = setTimeout(() => {
                        ws.close();
                        console.warn('⚠️ WebSocket连接超时');
                        resolve(false);
                    }, 5000);
                    
                    ws.onopen = function() {
                        clearTimeout(timeout);
                        console.log('✅ WebSocket连接正常');
                        ws.close();
                        resolve(true);
                    };
                    
                    ws.onerror = function(error) {
                        clearTimeout(timeout);
                        console.error('❌ WebSocket连接失败:', error);
                        resolve(false);
                    };
                });
                
            } catch (error) {
                console.error('❌ 连接测试失败:', error);
                return false;
            }
        }
    };

    // 全局暴露
    window.CloudflareConfig = CloudflareConfig;

    // 自动初始化和调试输出
    document.addEventListener('DOMContentLoaded', function() {
        const config = CloudflareConfig.getConfig();
        console.log('🚀 Cloudflare配置已加载:', config.mode);
        
        // 开发模式下显示详细信息
        if (window.location.search.includes('debug=true')) {
            CloudflareConfig.debug();
        }
    });

})(window);
