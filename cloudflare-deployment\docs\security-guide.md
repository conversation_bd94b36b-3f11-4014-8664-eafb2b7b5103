# 安全配置指南

## 🔒 安全概述

本指南提供MCP Feedback Enhanced Cloudflare部署的安全最佳实践。

## 🛡️ 基础安全配置

### 1. SSL/TLS设置

#### Cloudflare Dashboard配置
1. **SSL/TLS模式**
   - 设置为 "Full (strict)"
   - 启用 "Always Use HTTPS"
   - 启用 "Automatic HTTPS Rewrites"

2. **最小TLS版本**
   ```
   SSL/TLS → Edge Certificates → Minimum TLS Version: 1.2
   ```

3. **HSTS设置**
   ```
   SSL/TLS → Edge Certificates → HTTP Strict Transport Security (HSTS)
   - Enable HSTS: On
   - Max Age Header: 6 months
   - Include Subdomains: On
   - Preload: On
   ```

#### 代码配置
更新 `pages/_headers`：
```
/*
  Strict-Transport-Security: max-age=31536000; includeSubDomains; preload
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' wss: https:; img-src 'self' data:; font-src 'self'
```

### 2. 访问控制

#### IP白名单（可选）
```yaml
# tunnel/config.yml 添加
ingress:
  - hostname: your-domain.com
    service: http://localhost:8765
    originRequest:
      # 仅允许特定IP访问
      access:
        required: true
        aud:
          - "your-access-policy-aud"
```

#### Cloudflare Access集成
1. **创建Access应用**
   ```bash
   # 使用wrangler创建Access应用
   wrangler access app create mcp-feedback-enhanced \
     --domain your-domain.com \
     --type self_hosted
   ```

2. **配置认证策略**
   - 邮箱域名限制
   - Google/GitHub OAuth
   - 一次性PIN码

### 3. WAF规则

#### 基础防护规则
在Cloudflare Dashboard → Security → WAF中配置：

1. **SQL注入防护**
   ```
   (http.request.uri.query contains "union select") or 
   (http.request.uri.query contains "drop table") or
   (http.request.body contains "union select")
   ```

2. **XSS防护**
   ```
   (http.request.uri.query contains "<script") or
   (http.request.body contains "<script") or
   (http.request.uri.query contains "javascript:")
   ```

3. **文件上传限制**
   ```
   (http.request.method eq "POST") and 
   (http.request.uri.path contains "/upload") and
   (http.request.body.size gt 10485760)
   ```

## 🔐 高级安全配置

### 1. 端到端加密

#### WebSocket安全
```javascript
// cloudflare-config.js 中确保使用WSS
getWebSocketUrl: function() {
    const protocol = 'wss:'; // 强制使用安全连接
    // ... 其他代码
}
```

#### API请求加密
```javascript
// 添加请求头验证
const apiHeaders = {
    'X-Requested-With': 'XMLHttpRequest',
    'X-CSRF-Token': generateCSRFToken(),
    'Content-Type': 'application/json'
};
```

### 2. 速率限制

#### Cloudflare Rate Limiting
```json
{
  "id": "rate_limit_api",
  "match": {
    "request": {
      "url": "https://api.your-domain.com/*"
    }
  },
  "threshold": 100,
  "period": 60,
  "action": {
    "mode": "challenge"
  }
}
```

#### 应用层限制
在MCP服务器中添加：
```python
# 示例：在FastAPI中添加速率限制
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/api/feedback")
@limiter.limit("10/minute")
async def submit_feedback(request: Request):
    # 处理反馈提交
    pass
```

### 3. 数据保护

#### 敏感数据处理
```javascript
// 客户端数据清理
function sanitizeInput(input) {
    return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '');
}
```

#### 会话安全
```python
# 服务器端会话管理
import secrets
import hashlib

def generate_session_token():
    return secrets.token_urlsafe(32)

def hash_session_id(session_id):
    return hashlib.sha256(session_id.encode()).hexdigest()
```

## 📊 安全监控

### 1. 日志配置

#### Cloudflare日志
```yaml
# tunnel/config.yml
logLevel: info
logFile: /var/log/cloudflared.log

# 日志轮转配置
logrotate:
  - path: /var/log/cloudflared.log
    maxSize: 100MB
    maxAge: 30
    compress: true
```

#### 应用日志
```python
import logging

# 安全事件日志
security_logger = logging.getLogger('security')
security_logger.setLevel(logging.WARNING)

def log_security_event(event_type, details):
    security_logger.warning(f"Security Event: {event_type} - {details}")
```

### 2. 异常检测

#### 自动化监控脚本
```bash
#!/bin/bash
# security-monitor.sh

# 检查异常连接
netstat -an | grep :8765 | wc -l > /tmp/connections.log

# 检查失败的认证尝试
grep "authentication failed" /var/log/cloudflared.log | tail -10

# 检查大量请求
tail -1000 /var/log/cloudflared.log | grep -c "POST" > /tmp/post_requests.log
```

### 3. 告警配置

#### Cloudflare Analytics告警
```json
{
  "name": "High Error Rate Alert",
  "conditions": {
    "error_rate": "> 5%",
    "time_window": "5m"
  },
  "actions": {
    "email": "<EMAIL>",
    "webhook": "https://your-webhook-url.com/alert"
  }
}
```

## 🔧 安全维护

### 1. 定期安全检查

#### 每日检查清单
- [ ] 检查SSL证书状态
- [ ] 查看访问日志异常
- [ ] 验证WAF规则效果
- [ ] 监控错误率

#### 每周检查清单
- [ ] 更新安全规则
- [ ] 检查依赖项漏洞
- [ ] 审查访问权限
- [ ] 备份安全配置

#### 每月检查清单
- [ ] 安全扫描
- [ ] 渗透测试
- [ ] 更新安全策略
- [ ] 培训团队成员

### 2. 应急响应

#### 安全事件响应流程
1. **立即响应**
   ```bash
   # 临时阻止可疑IP
   wrangler firewall rule create --expression "ip.src eq *************" --action block
   
   # 启用Under Attack模式
   wrangler zone setting update --name security_level --value under_attack
   ```

2. **调查分析**
   ```bash
   # 导出访问日志
   wrangler logs tail --format json > security_incident.log
   
   # 分析异常模式
   grep "suspicious_pattern" security_incident.log
   ```

3. **恢复和加固**
   - 修复安全漏洞
   - 更新安全规则
   - 加强监控
   - 文档记录

## 📋 安全检查清单

### 部署前检查
- [ ] SSL/TLS配置正确
- [ ] WAF规则已启用
- [ ] 访问控制已配置
- [ ] 敏感信息已移除
- [ ] 安全头部已设置

### 运行时检查
- [ ] 监控告警正常
- [ ] 日志记录完整
- [ ] 访问模式正常
- [ ] 性能指标健康
- [ ] 备份策略有效

### 定期审计
- [ ] 安全策略更新
- [ ] 权限最小化
- [ ] 依赖项安全
- [ ] 配置合规性
- [ ] 事件响应测试

---

**🔒 安全是一个持续的过程，请定期审查和更新安全配置。**
