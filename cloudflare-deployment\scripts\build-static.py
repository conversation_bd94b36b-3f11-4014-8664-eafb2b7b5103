#!/usr/bin/env python3
"""
静态资源构建脚本
用于将MCP Feedback Enhanced的Web UI资源复制到Cloudflare Pages目录
"""

import os
import shutil
import json
import argparse
from pathlib import Path

# 颜色输出
class Colors:
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'

def log_info(message):
    print(f"{Colors.BLUE}[INFO]{Colors.ENDC} {message}")

def log_success(message):
    print(f"{Colors.GREEN}[SUCCESS]{Colors.ENDC} {message}")

def log_warning(message):
    print(f"{Colors.YELLOW}[WARNING]{Colors.ENDC} {message}")

def log_error(message):
    print(f"{Colors.RED}[ERROR]{Colors.ENDC} {message}")

def get_project_root():
    """获取项目根目录"""
    current_dir = Path(__file__).parent
    # 向上查找直到找到包含src目录的根目录
    while current_dir.parent != current_dir:
        if (current_dir / "src" / "mcp_feedback_enhanced").exists():
            return current_dir
        current_dir = current_dir.parent
    
    raise FileNotFoundError("无法找到项目根目录")

def copy_static_files(src_dir, dest_dir):
    """复制静态文件"""
    log_info(f"复制静态文件: {src_dir} -> {dest_dir}")
    
    if not src_dir.exists():
        log_warning(f"源目录不存在: {src_dir}")
        return False
    
    # 创建目标目录
    dest_dir.mkdir(parents=True, exist_ok=True)
    
    # 复制文件
    try:
        if dest_dir.exists():
            shutil.rmtree(dest_dir)
        shutil.copytree(src_dir, dest_dir)
        log_success(f"静态文件复制完成: {dest_dir}")
        return True
    except Exception as e:
        log_error(f"复制静态文件失败: {e}")
        return False

def create_redirects_file(pages_dir):
    """创建Cloudflare Pages重定向规则"""
    redirects_content = """# Cloudflare Pages重定向规则

# API请求重定向到Tunnel
/api/* https://api.your-domain.com/:splat 200
/ws https://ws.your-domain.com/ws 200

# 静态资源
/static/* /static/:splat 200

# SPA路由 - 所有其他请求重定向到index.html
/* /index.html 200
"""
    
    redirects_file = pages_dir / "_redirects"
    try:
        with open(redirects_file, 'w', encoding='utf-8') as f:
            f.write(redirects_content)
        log_success(f"重定向规则文件已创建: {redirects_file}")
        return True
    except Exception as e:
        log_error(f"创建重定向规则文件失败: {e}")
        return False

def create_headers_file(pages_dir):
    """创建Cloudflare Pages头部规则"""
    headers_content = """# Cloudflare Pages头部规则

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin

/static/*
  Cache-Control: public, max-age=31536000, immutable

/*.js
  Cache-Control: public, max-age=86400

/*.css
  Cache-Control: public, max-age=86400

/*.html
  Cache-Control: public, max-age=3600
"""
    
    headers_file = pages_dir / "_headers"
    try:
        with open(headers_file, 'w', encoding='utf-8') as f:
            f.write(headers_content)
        log_success(f"头部规则文件已创建: {headers_file}")
        return True
    except Exception as e:
        log_error(f"创建头部规则文件失败: {e}")
        return False

def update_asset_paths(pages_dir):
    """更新资源路径为相对路径"""
    log_info("更新资源路径...")
    
    # 更新HTML文件中的路径
    html_files = list(pages_dir.glob("*.html"))
    
    for html_file in html_files:
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换绝对路径为相对路径
            content = content.replace('href="/static/', 'href="./static/')
            content = content.replace('src="/static/', 'src="./static/')
            
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            log_success(f"已更新资源路径: {html_file.name}")
            
        except Exception as e:
            log_error(f"更新资源路径失败 {html_file}: {e}")

def create_package_json(pages_dir):
    """创建package.json用于Cloudflare Pages构建"""
    package_json = {
        "name": "mcp-feedback-enhanced-cloudflare",
        "version": "1.0.0",
        "description": "MCP Feedback Enhanced - Cloudflare Pages部署",
        "scripts": {
            "build": "echo 'Static files ready'",
            "start": "echo 'Static site'"
        },
        "keywords": ["mcp", "feedback", "cloudflare", "pages"],
        "author": "Minidoracat",
        "license": "MIT"
    }
    
    package_file = pages_dir / "package.json"
    try:
        with open(package_file, 'w', encoding='utf-8') as f:
            json.dump(package_json, f, indent=2, ensure_ascii=False)
        log_success(f"package.json已创建: {package_file}")
        return True
    except Exception as e:
        log_error(f"创建package.json失败: {e}")
        return False

def main():
    parser = argparse.ArgumentParser(description="构建MCP Feedback Enhanced的Cloudflare Pages静态资源")
    parser.add_argument("--clean", action="store_true", help="清理目标目录")
    parser.add_argument("--verbose", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    try:
        # 获取路径
        project_root = get_project_root()
        cloudflare_dir = project_root / "cloudflare-deployment"
        pages_dir = cloudflare_dir / "pages"
        src_static_dir = project_root / "src" / "mcp_feedback_enhanced" / "web" / "static"
        
        log_info(f"项目根目录: {project_root}")
        log_info(f"Cloudflare部署目录: {cloudflare_dir}")
        log_info(f"Pages目录: {pages_dir}")
        log_info(f"源静态文件目录: {src_static_dir}")
        
        # 清理目标目录
        if args.clean and pages_dir.exists():
            log_info("清理目标目录...")
            shutil.rmtree(pages_dir / "static", ignore_errors=True)
        
        # 创建Pages目录
        pages_dir.mkdir(parents=True, exist_ok=True)
        
        # 复制静态文件
        if src_static_dir.exists():
            dest_static_dir = pages_dir / "static"
            if copy_static_files(src_static_dir, dest_static_dir):
                log_success("静态文件复制完成")
            else:
                log_error("静态文件复制失败")
                return False
        else:
            log_warning(f"源静态文件目录不存在: {src_static_dir}")
        
        # 更新资源路径
        update_asset_paths(pages_dir)
        
        # 创建配置文件
        create_redirects_file(pages_dir)
        create_headers_file(pages_dir)
        create_package_json(pages_dir)
        
        log_success("🎉 静态资源构建完成！")
        log_info("下一步:")
        log_info("1. 检查 cloudflare-deployment/pages/ 目录")
        log_info("2. 运行部署脚本: ./deploy-pages.sh")
        log_info("3. 或手动上传到Cloudflare Pages")
        
        return True
        
    except Exception as e:
        log_error(f"构建失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
