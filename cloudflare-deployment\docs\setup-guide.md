# MCP Feedback Enhanced - Cloudflare部署完整指南

## 🎯 概述

本指南将帮助您将MCP Feedback Enhanced部署到Cloudflare，实现全球访问和高性能。

## 📋 前置要求

### 1. 账户和工具
- Cloudflare账户（免费套餐即可）
- 域名（可选，也可使用Cloudflare提供的子域名）
- Node.js 和 npm（用于安装Cloudflare CLI）

### 2. 安装Cloudflare CLI
```bash
npm install -g wrangler
```

## 🚀 部署步骤

### 步骤1：设置Cloudflare Tunnel

1. **登录Cloudflare**
   ```bash
   wrangler login
   ```

2. **创建Tunnel**
   ```bash
   cd cloudflare-deployment/tunnel
   cloudflared tunnel login
   cloudflared tunnel create mcp-feedback-enhanced
   ```

3. **配置DNS**
   ```bash
   # 替换YOUR_TUNNEL_ID为实际的Tunnel ID
   cloudflared tunnel route dns YOUR_TUNNEL_ID your-domain.com
   cloudflared tunnel route dns YOUR_TUNNEL_ID api.your-domain.com
   cloudflared tunnel route dns YOUR_TUNNEL_ID ws.your-domain.com
   ```

4. **更新配置文件**
   编辑 `tunnel/config.yml`，替换以下内容：
   - `YOUR_TUNNEL_ID` → 实际的Tunnel ID
   - `your-domain.com` → 您的域名
   - `/path/to/credentials.json` → 实际的凭证文件路径

### 步骤2：部署Cloudflare Pages

1. **构建静态资源**
   ```bash
   cd cloudflare-deployment/scripts
   ./deploy-pages.sh
   ```

2. **或手动部署**
   ```bash
   cd cloudflare-deployment/pages
   wrangler pages deploy . --project-name=mcp-feedback-enhanced
   ```

### 步骤3：启动服务

1. **启动本地MCP服务器**
   ```bash
   # 设置环境变量
   export CLOUDFLARE_MODE=true
   export MCP_WEB_PORT=8765
   
   # 启动服务器
   uvx mcp-feedback-enhanced@latest test --web
   ```

2. **启动Cloudflare Tunnel**
   ```bash
   cd cloudflare-deployment/tunnel
   cloudflared tunnel --config config.yml run
   ```

### 步骤4：测试连接

1. **访问部署的URL**
   - Cloudflare Pages URL: `https://mcp-feedback-enhanced.pages.dev`
   - 自定义域名: `https://your-domain.com`

2. **检查连接状态**
   - 页面右上角应显示绿色连接指示器
   - 左下角环境信息应显示"cloudflare"模式

## ⚙️ 配置选项

### 环境变量

在MCP配置中添加以下环境变量：

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "env": {
        "CLOUDFLARE_MODE": "true",
        "MCP_WEB_PORT": "8765",
        "MCP_DEBUG": "false"
      },
      "autoApprove": ["interactive_feedback"]
    }
  }
}
```

### Tunnel配置优化

编辑 `tunnel/config.yml` 进行性能优化：

```yaml
# 连接优化
originRequest:
  connectTimeout: 30s
  tlsTimeout: 10s
  tcpKeepAlive: 30s
  keepAliveConnections: 10
  keepAliveTimeout: 90s

# 日志级别
logLevel: info

# 自动更新
autoupdate-freq: 24h
```

## 🔧 故障排除

### 常见问题

1. **WebSocket连接失败**
   - 检查Tunnel是否正常运行
   - 确认DNS记录已正确配置
   - 验证本地MCP服务器是否启动

2. **页面无法加载**
   - 检查Cloudflare Pages部署状态
   - 确认静态文件已正确上传
   - 查看浏览器控制台错误信息

3. **API请求失败**
   - 确认API子域名DNS配置
   - 检查Tunnel配置中的服务地址
   - 验证本地服务器端口设置

### 调试工具

1. **启用调试模式**
   在URL后添加 `?debug=true` 查看详细信息

2. **检查连接状态**
   ```bash
   # 检查Tunnel状态
   cloudflared tunnel info YOUR_TUNNEL_ID
   
   # 查看Tunnel日志
   cloudflared tunnel --config config.yml run --loglevel debug
   ```

3. **测试本地连接**
   ```bash
   curl http://localhost:8765/health
   ```

## 📊 监控和维护

### 性能监控
- Cloudflare Analytics：查看访问统计
- Tunnel指标：监控连接质量
- 本地日志：检查MCP服务器状态

### 定期维护
- 更新Cloudflare CLI和cloudflared
- 检查SSL证书状态
- 监控域名DNS配置

## 🔒 安全建议

1. **访问控制**
   - 配置Cloudflare Access（可选）
   - 设置IP白名单
   - 启用WAF规则

2. **SSL/TLS**
   - 使用Full (strict) SSL模式
   - 启用HSTS
   - 配置最小TLS版本

3. **监控**
   - 启用安全事件通知
   - 定期检查访问日志
   - 监控异常流量

## 📞 支持

如遇问题，请：
1. 查看项目GitHub Issues
2. 检查Cloudflare文档
3. 联系技术支持

---

**🎉 恭喜！您已成功部署MCP Feedback Enhanced到Cloudflare！**
