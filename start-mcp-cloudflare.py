#!/usr/bin/env python3
"""
启动MCP Feedback Enhanced服务器 - Cloudflare模式
支持与Cloudflare Workers的集成
"""

import os
import sys
import asyncio
from pathlib import Path

# 添加项目路径到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root / "src"))

# 设置Cloudflare模式环境变量
os.environ["CLOUDFLARE_MODE"] = "true"
os.environ["MCP_WEB_PORT"] = "8765"
os.environ["MCP_DEBUG"] = "true"

# 导入MCP服务器
from mcp_feedback_enhanced.server import main

if __name__ == "__main__":
    print("🚀 启动MCP Feedback Enhanced - Cloudflare模式")
    print(f"📁 项目目录: {project_root}")
    print(f"🌐 Cloudflare Workers URL: https://mcp-feedback-enhanced.sujianjob.workers.dev")
    print(f"🔧 本地端口: 8765")
    print("=" * 60)
    
    # 启动MCP服务器
    main()
