# Cloudflare Tunnel配置文件
# 用于将本地MCP服务器安全地暴露到云端

tunnel: YOUR_TUNNEL_ID  # 替换为实际的Tunnel ID
credentials-file: /path/to/credentials.json  # 替换为实际的凭证文件路径

# 入口规则配置
ingress:
  # 主域名 - 处理所有Web UI请求
  - hostname: your-domain.com
    service: http://localhost:8765
    originRequest:
      # WebSocket支持
      noTLSVerify: false
      connectTimeout: 30s
      tlsTimeout: 10s
      tcpKeepAlive: 30s
      keepAliveConnections: 10
      keepAliveTimeout: 90s
      httpHostHeader: localhost:8765
      
  # API子域名 - 专门处理API请求
  - hostname: api.your-domain.com
    service: http://localhost:8765
    originRequest:
      noTLSVerify: false
      connectTimeout: 30s
      
  # WebSocket子域名 - 专门处理WebSocket连接
  - hostname: ws.your-domain.com
    service: http://localhost:8765
    originRequest:
      noTLSVerify: false
      connectTimeout: 30s
      
  # 默认规则 - 返回404
  - service: http_status:404

# 日志配置
logLevel: info
logFile: /var/log/cloudflared.log

# 指标配置
metrics: 0.0.0.0:8080

# 自动更新
autoupdate-freq: 24h

# 重试配置
retries: 3

# 优雅关闭超时
grace-period: 30s
