# 详细设置指南

## 🎯 简化架构说明

```
用户浏览器 → Cloudflare Workers → ngrok → 本地MCP服务器
```

**工作流程**：
1. 用户访问Cloudflare Workers URL
2. Workers提供Web UI界面
3. 用户操作通过WebSocket发送到Workers
4. Workers转发消息到ngrok暴露的本地服务器
5. 本地MCP服务器处理请求并返回结果

## 📋 详细步骤

### 步骤1：环境准备

#### 1.1 安装必要工具
```bash
# 安装Cloudflare CLI
npm install -g wrangler

# 安装ngrok（选择一种方式）
# macOS
brew install ngrok

# Windows
choco install ngrok

# Linux
snap install ngrok

# 或从官网下载：https://ngrok.com/download
```

#### 1.2 账户设置
```bash
# 登录Cloudflare
wrangler login

# 登录ngrok（需要免费账户）
ngrok authtoken YOUR_NGROK_TOKEN
```

### 步骤2：启动本地服务

#### 2.1 启动MCP服务器
```bash
# 方式1：使用uvx（推荐）
uvx mcp-feedback-enhanced@latest test --web

# 方式2：如果已安装
python -m mcp_feedback_enhanced test --web

# 验证服务器运行
curl http://localhost:8765/health
```

#### 2.2 使用ngrok暴露服务器
```bash
# 在新终端运行
ngrok http --url=workable-urchin-together.ngrok-free.app 8765 

# 记录输出中的HTTPS URL，例如：
# https://workable-urchin-together.ngrok-free.app
```

### 步骤3：部署Workers

#### 3.1 自动部署（推荐）
```bash
cd cloudflare-workers-simple
./deploy.sh
```

#### 3.2 手动部署
```bash
cd cloudflare-workers-simple

# 设置本地服务器地址
wrangler secret put LOCAL_MCP_URL
# 输入：wss://workable-urchin-together.ngrok-free.app

# 部署Workers
wrangler deploy

# 获取Workers URL
wrangler subdomain get
```

### 步骤4：测试和使用

#### 4.1 访问应用
1. 打开Workers提供的URL（如：https://mcp-feedback.your-subdomain.workers.dev）
2. 页面应显示"等待MCP会话..."
3. 右上角连接状态应为绿色

#### 4.2 测试MCP集成
1. 在AI助手中调用MCP工具
2. Workers页面应显示AI工作摘要
3. 输入反馈并提交测试

## ⚙️ 高级配置

### 自定义域名
```bash
# 添加自定义域名
wrangler route add "your-domain.com/*" mcp-feedback-enhanced

# 或在wrangler.toml中配置
routes = [
  { pattern = "your-domain.com/*", zone_name = "your-domain.com" }
]
```

### 环境变量管理
```bash
# 查看当前配置
wrangler secret list

# 更新本地服务器地址
wrangler secret put LOCAL_MCP_URL

# 启用调试模式
wrangler secret put DEBUG_MODE
# 输入：true
```

### 性能优化
```toml
# wrangler.toml
[limits]
cpu_ms = 50  # CPU时间限制

[env.production]
name = "mcp-feedback-enhanced-prod"
```

## 🔧 故障排除

### 常见问题

#### 1. WebSocket连接失败
**症状**：页面显示"连接错误"

**解决方案**：
```bash
# 检查ngrok状态
curl https://your-ngrok-url.ngrok.io/health

# 检查环境变量
wrangler secret list

# 查看Workers日志
wrangler tail
```

#### 2. 本地服务器无响应
**症状**：ngrok连接失败

**解决方案**：
```bash
# 检查本地服务器
curl http://localhost:8765/health

# 重启MCP服务器
uvx mcp-feedback-enhanced@latest test --web

# 检查端口占用
netstat -an | grep 8765
```

#### 3. Workers部署失败
**症状**：wrangler deploy报错

**解决方案**：
```bash
# 检查登录状态
wrangler whoami

# 重新登录
wrangler login

# 检查配置文件
cat wrangler.toml
```

### 调试工具

#### 实时日志
```bash
# 查看Workers日志
wrangler tail

# 过滤错误日志
wrangler tail --format=pretty | grep ERROR
```

#### 本地测试
```bash
# 测试本地连接
curl -v http://localhost:8765/health

# 测试ngrok连接
curl -v https://your-ngrok-url.ngrok.io/health

# 测试Workers健康检查
curl -v https://your-workers-url.workers.dev/health
```

## 📊 监控和维护

### 日常检查
- [ ] 本地MCP服务器运行状态
- [ ] ngrok连接状态
- [ ] Workers部署状态
- [ ] 用户访问正常

### 定期维护
```bash
# 更新Workers
wrangler deploy

# 检查ngrok账户状态
ngrok account

# 更新MCP服务器
uvx mcp-feedback-enhanced@latest test --web
```

### 备份和恢复
```bash
# 备份配置
cp wrangler.toml wrangler.toml.backup

# 导出环境变量（手动记录）
wrangler secret list

# 恢复部署
wrangler deploy
```

## 🔒 安全考虑

### ngrok安全
- 使用ngrok账户获得固定域名
- 启用ngrok的基础认证（可选）
- 定期更换ngrok URL

### Workers安全
- 限制访问来源（可选）
- 启用Cloudflare安全功能
- 监控异常访问

### 本地安全
- 确保本地防火墙配置正确
- 不要在生产环境暴露调试端口
- 定期更新MCP服务器

---

**🎉 享受简单的全球部署体验！**
