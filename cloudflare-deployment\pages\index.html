<!DOCTYPE html>
<html lang="zh-TW" id="html-root">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Feedback Enhanced - Cloudflare</title>
    
    <!-- Cloudflare配置 -->
    <script src="./cloudflare-config.js"></script>
    
    <!-- 原有样式 -->
    <link rel="stylesheet" href="./static/css/styles.css">
    <style>
        :root {
            /* 深色主题颜色变量 */
            --bg-primary: #1e1e1e;
            --bg-secondary: #2d2d30;
            --bg-tertiary: #252526;
            --surface-color: #333333;
            --text-primary: #cccccc;
            --text-secondary: #9e9e9e;
            --accent-color: #007acc;
            --accent-hover: #005a9e;
            --border-color: #464647;
            --success-color: #4caf50;
            --warning-color: #ff9800;
            --error-color: #f44336;
            --info-color: #2196f3;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Cloudflare环境指示器 */
        .cloudflare-indicator {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: linear-gradient(135deg, #f38020, #f5af19);
            border-radius: 6px;
            font-size: 12px;
            font-weight: 600;
            color: white;
            box-shadow: 0 2px 8px rgba(243, 128, 32, 0.3);
        }

        .cloudflare-indicator::before {
            content: "☁️";
            font-size: 14px;
        }

        /* 等待会话时的样式 */
        .waiting-container {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            text-align: center;
        }

        .waiting-content {
            max-width: 600px;
            padding: 40px;
            background: var(--bg-secondary);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .waiting-title {
            font-size: 2.5em;
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 20px;
        }

        .waiting-description {
            color: var(--text-secondary);
            font-size: 1.2em;
            margin-bottom: 30px;
        }

        .waiting-status {
            padding: 20px;
            background: rgba(0, 122, 204, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: 8px;
            color: var(--accent-color);
        }

        /* 连接状态指示器 */
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }

        .connection-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--error-color);
            transition: all 0.3s ease;
        }

        .connection-indicator.connected {
            background: var(--success-color);
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
        }

        .connection-indicator.connecting {
            background: var(--warning-color);
            animation: pulse 1s infinite;
        }

        .connection-indicator.error {
            background: var(--error-color);
        }

        .connection-indicator.disconnected {
            background: var(--text-secondary);
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 主容器 - 有会话时显示 */
        .main-container {
            display: none;
            max-width: 1200px;
            width: 100%;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
            min-height: 100vh;
            flex-direction: column;
        }

        .main-container.active {
            display: flex;
        }

        /* 环境信息面板 */
        .environment-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            padding: 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 11px;
            color: var(--text-secondary);
            max-width: 300px;
            opacity: 0.8;
            transition: opacity 0.3s ease;
        }

        .environment-info:hover {
            opacity: 1;
        }

        .environment-info h4 {
            color: var(--accent-color);
            margin-bottom: 8px;
            font-size: 12px;
        }

        .environment-info .info-item {
            margin-bottom: 4px;
        }

        .environment-info .info-label {
            font-weight: 600;
            color: var(--text-primary);
        }

        /* 调试面板 */
        .debug-panel {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
            padding: 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            font-size: 11px;
            color: var(--text-secondary);
            display: none;
        }

        .debug-panel.show {
            display: block;
        }

        .debug-panel button {
            background: var(--accent-color);
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            cursor: pointer;
            margin: 2px;
        }

        .debug-panel button:hover {
            background: var(--accent-hover);
        }
    </style>
</head>
<body>
    <!-- Cloudflare环境指示器 -->
    <div class="cloudflare-indicator" id="cloudflareIndicator" style="display: none;">
        Cloudflare Pages
    </div>

    <!-- 连接状态指示器 -->
    <div class="connection-status">
        <div class="connection-indicator" id="connectionIndicator"></div>
        <span id="connectionText">连接中...</span>
    </div>

    <!-- 等待会话的页面 -->
    <div class="waiting-container" id="waitingContainer">
        <div class="waiting-content">
            <h1 class="waiting-title">MCP Feedback Enhanced</h1>
            <p class="waiting-description">
                Web UI 互动式反馈收集工具 - Cloudflare版
            </p>
            <div class="waiting-status">
                等待 MCP 服务调用以建立反馈会话...
            </div>
        </div>
    </div>

    <!-- 主要反馈界面 -->
    <div class="main-container" id="mainContainer">
        <!-- 这里将动态加载反馈界面内容 -->
    </div>

    <!-- 环境信息面板 -->
    <div class="environment-info" id="environmentInfo">
        <h4>🌐 环境信息</h4>
        <div class="info-item">
            <span class="info-label">模式:</span> 
            <span id="envMode">检测中...</span>
        </div>
        <div class="info-item">
            <span class="info-label">API:</span> 
            <span id="envApiUrl">-</span>
        </div>
        <div class="info-item">
            <span class="info-label">WebSocket:</span> 
            <span id="envWsUrl">-</span>
        </div>
    </div>

    <!-- 调试面板 -->
    <div class="debug-panel" id="debugPanel">
        <h4>🔧 调试工具</h4>
        <button onclick="CloudflareConfig.debug()">显示配置</button>
        <button onclick="CloudflareConfig.testConnection()">测试连接</button>
        <button onclick="toggleCloudflareMode()">切换模式</button>
    </div>

    <!-- JavaScript -->
    <script>
        // 初始化环境检测
        document.addEventListener('DOMContentLoaded', function() {
            // 显示Cloudflare指示器
            if (window.CloudflareConfig && window.CloudflareConfig.isCloudflareMode()) {
                document.getElementById('cloudflareIndicator').style.display = 'flex';
            }

            // 更新环境信息
            updateEnvironmentInfo();

            // 显示调试面板（如果URL包含debug参数）
            if (window.location.search.includes('debug=true')) {
                document.getElementById('debugPanel').classList.add('show');
            }
        });

        // 更新环境信息
        function updateEnvironmentInfo() {
            if (window.CloudflareConfig) {
                const config = window.CloudflareConfig.getConfig();
                document.getElementById('envMode').textContent = config.mode;
                document.getElementById('envApiUrl').textContent = config.apiBaseUrl;
                document.getElementById('envWsUrl').textContent = config.webSocketUrl;
            }
        }

        // 切换Cloudflare模式
        function toggleCloudflareMode() {
            if (window.CloudflareConfig) {
                const currentMode = window.CloudflareConfig.isCloudflareMode();
                window.CloudflareConfig.setCloudflareMode(!currentMode);
                location.reload();
            }
        }
    </script>

    <!-- 加载原有的JavaScript模块 -->
    <script src="./static/js/i18n.js"></script>
    <script src="./static/js/modules/utils.js"></script>
    <script src="./static/js/modules/tab-manager.js"></script>
    
    <!-- 使用Cloudflare适配版WebSocket管理器 -->
    <script src="./static/js/websocket-manager-cloudflare.js"></script>
    
    <script src="./static/js/modules/image-handler.js"></script>
    <script src="./static/js/modules/settings-manager.js"></script>
    <script src="./static/js/modules/ui-manager.js"></script>
    <script src="./static/js/modules/auto-refresh-manager.js"></script>
    
    <!-- 主应用程序 -->
    <script src="./static/js/app.js"></script>
</body>
</html>
