/**
 * Cloudflare适配版WebSocket管理器
 * 基于原版WebSocketManager，增加云端部署支持
 */

(function() {
    'use strict';

    // 确保命名空间存在
    if (!window.MCPFeedback) {
        window.MCPFeedback = {};
    }

    /**
     * Cloudflare适配版WebSocket管理器
     */
    function CloudflareWebSocketManager(options) {
        options = options || {};

        // 继承原有属性
        this.websocket = null;
        this.isConnected = false;
        this.connectionReady = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.reconnectDelay = options.reconnectDelay || 1000;
        this.heartbeatInterval = null;
        this.heartbeatFrequency = options.heartbeatFrequency || 30000;
        this.networkOnline = navigator.onLine;

        // 事件回调
        this.onOpen = options.onOpen || null;
        this.onMessage = options.onMessage || null;
        this.onClose = options.onClose || null;
        this.onError = options.onError || null;
        this.onConnectionStatusChange = options.onConnectionStatusChange || null;

        // 引用其他管理器
        this.tabManager = options.tabManager || null;
        this.connectionMonitor = options.connectionMonitor || null;

        // Cloudflare特有属性
        this.pendingSubmission = null;
        this.sessionUpdatePending = false;

        // 初始化网络状态检测
        this.setupNetworkStatusDetection();

        console.log('🌐 CloudflareWebSocketManager 初始化完成');
    }

    /**
     * 获取WebSocket URL - Cloudflare适配版
     */
    CloudflareWebSocketManager.prototype.getWebSocketUrl = function() {
        // 使用Cloudflare配置管理器
        if (window.CloudflareConfig) {
            const wsUrl = window.CloudflareConfig.getWebSocketUrl();
            console.log('🔗 使用Cloudflare配置的WebSocket URL:', wsUrl);
            return wsUrl;
        }

        // 回退到原有逻辑
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        const wsUrl = protocol + '//' + host + '/ws';
        
        console.log('🔗 使用默认WebSocket URL:', wsUrl);
        return wsUrl;
    };

    /**
     * 建立WebSocket连接 - Cloudflare适配版
     */
    CloudflareWebSocketManager.prototype.connect = function() {
        if (!this.isWebSocketSupported()) {
            console.error('❌ 浏览器不支持WebSocket');
            return;
        }

        const wsUrl = this.getWebSocketUrl();
        console.log('🔌 尝试连接WebSocket:', wsUrl);
        
        const connectingMessage = window.i18nManager ? 
            window.i18nManager.t('connectionMonitor.connecting') : '连接中...';
        this.updateConnectionStatus('connecting', connectingMessage);

        try {
            // 如果已有连接，先关闭
            if (this.websocket) {
                this.websocket.close();
                this.websocket = null;
            }

            this.websocket = new WebSocket(wsUrl);
            this.setupWebSocketEvents();

        } catch (error) {
            console.error('❌ WebSocket连接失败:', error);
            const connectionFailedMessage = window.i18nManager ? 
                window.i18nManager.t('connectionMonitor.connectionFailed') : '连接失败';
            this.updateConnectionStatus('error', connectionFailedMessage);
            
            // Cloudflare环境下的特殊处理
            this.handleCloudflareConnectionError(error);
        }
    };

    /**
     * 处理Cloudflare连接错误
     */
    CloudflareWebSocketManager.prototype.handleCloudflareConnectionError = function(error) {
        if (window.CloudflareConfig && window.CloudflareConfig.isCloudflareMode()) {
            console.warn('⚠️ Cloudflare环境连接失败，可能的原因：');
            console.warn('1. Tunnel未启动或配置错误');
            console.warn('2. 域名DNS配置问题');
            console.warn('3. 本地MCP服务器未运行');
            
            // 尝试连接测试
            if (window.CloudflareConfig.testConnection) {
                window.CloudflareConfig.testConnection().then(function(success) {
                    if (!success) {
                        console.error('❌ Cloudflare连接测试失败');
                    }
                });
            }
        }
    };

    /**
     * 检查WebSocket支持
     */
    CloudflareWebSocketManager.prototype.isWebSocketSupported = function() {
        return 'WebSocket' in window;
    };

    /**
     * 设置WebSocket事件监听器
     */
    CloudflareWebSocketManager.prototype.setupWebSocketEvents = function() {
        const self = this;

        this.websocket.onopen = function() {
            self.handleOpen();
        };

        this.websocket.onmessage = function(event) {
            self.handleMessage(event);
        };

        this.websocket.onclose = function(event) {
            self.handleClose(event);
        };

        this.websocket.onerror = function(error) {
            self.handleError(error);
        };
    };

    /**
     * 处理连接开启
     */
    CloudflareWebSocketManager.prototype.handleOpen = function() {
        this.isConnected = true;
        this.connectionReady = false;
        
        const connectedMessage = window.i18nManager ? 
            window.i18nManager.t('connectionMonitor.connected') : '已连接';
        this.updateConnectionStatus('connected', connectedMessage);
        
        console.log('✅ WebSocket连接已建立');

        // 重置重连计数器
        this.reconnectAttempts = 0;
        this.reconnectDelay = 1000;

        // 启动心跳
        this.startHeartbeat();

        // 请求会话状态
        this.requestSessionStatus();

        // 调用外部回调
        if (this.onOpen) {
            this.onOpen();
        }
    };

    /**
     * 处理消息接收
     */
    CloudflareWebSocketManager.prototype.handleMessage = function(event) {
        try {
            const data = JSON.parse(event.data);
            
            // 记录消息到监控器
            if (this.connectionMonitor) {
                this.connectionMonitor.recordMessage();
            }

            this.processMessage(data);

            // 调用外部回调
            if (this.onMessage) {
                this.onMessage(data);
            }
        } catch (error) {
            console.error('❌ 解析WebSocket消息失败:', error);
        }
    };

    /**
     * 处理连接关闭
     */
    CloudflareWebSocketManager.prototype.handleClose = function(event) {
        this.isConnected = false;
        this.connectionReady = false;
        
        console.log('🔌 WebSocket连接已关闭, code:', event.code, 'reason:', event.reason);

        // 停止心跳
        this.stopHeartbeat();

        // 处理不同的关闭原因
        if (event.code === 4004) {
            const noActiveSessionMessage = window.i18nManager ? 
                window.i18nManager.t('connectionMonitor.noActiveSession') : '没有活跃会话';
            this.updateConnectionStatus('disconnected', noActiveSessionMessage);
        } else {
            const disconnectedMessage = window.i18nManager ? 
                window.i18nManager.t('connectionMonitor.disconnected') : '已断开';
            this.updateConnectionStatus('disconnected', disconnectedMessage);
            this.handleReconnection(event);
        }

        // 调用外部回调
        if (this.onClose) {
            this.onClose(event);
        }
    };

    /**
     * 处理连接错误
     */
    CloudflareWebSocketManager.prototype.handleError = function(error) {
        console.error('❌ WebSocket错误:', error);
        
        const connectionErrorMessage = window.i18nManager ? 
            window.i18nManager.t('connectionMonitor.connectionError') : '连接错误';
        this.updateConnectionStatus('error', connectionErrorMessage);

        // 调用外部回调
        if (this.onError) {
            this.onError(error);
        }
    };

    /**
     * 处理重连逻辑 - Cloudflare优化版
     */
    CloudflareWebSocketManager.prototype.handleReconnection = function(event) {
        // 会话更新导致的正常关闭，立即重连
        if (event.code === 1000 && event.reason === '会话更新') {
            console.log('🔄 会话更新导致的连接关闭，立即重连...');
            this.sessionUpdatePending = true;
            const self = this;
            setTimeout(function() {
                self.connect();
            }, 200);
            return;
        }

        // 检查是否应该重连
        if (this.shouldAttemptReconnect(event)) {
            this.reconnectAttempts++;

            // Cloudflare环境下的智能重连策略
            const baseDelay = 1000;
            const exponentialDelay = baseDelay * Math.pow(2, this.reconnectAttempts - 1);
            const jitter = Math.random() * 1000;
            this.reconnectDelay = Math.min(exponentialDelay + jitter, 30000);

            console.log(`🔄 ${Math.round(this.reconnectDelay / 1000)}秒后尝试重连... (第${this.reconnectAttempts}次)`);

            const reconnectingTemplate = window.i18nManager ? 
                window.i18nManager.t('connectionMonitor.reconnecting') : '重连中... (第{attempt}次)';
            const reconnectingMessage = reconnectingTemplate.replace('{attempt}', this.reconnectAttempts);
            this.updateConnectionStatus('reconnecting', reconnectingMessage);

            const self = this;
            setTimeout(function() {
                console.log(`🔄 开始重连WebSocket... (第${self.reconnectAttempts}次)`);
                self.connect();
            }, this.reconnectDelay);
        }
    };

    // 继续添加其他必要的方法...
    // [由于字符限制，其他方法将在下一个文件中继续]

    // 将CloudflareWebSocketManager加入命名空间
    window.MCPFeedback.CloudflareWebSocketManager = CloudflareWebSocketManager;

    console.log('✅ CloudflareWebSocketManager模块加载完成');

})();
