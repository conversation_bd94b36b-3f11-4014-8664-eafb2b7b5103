/**
 * MCP Feedback Enhanced - Cloudflare Workers简化版
 * 
 * 功能：
 * 1. 托管Web UI界面
 * 2. WebSocket代理到本地MCP服务器
 * 3. 处理API请求转发
 */

// Web UI HTML模板
const HTML_TEMPLATE = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Feedback Enhanced - Workers版</title>
    <style>
        :root {
            --bg-primary: #1e1e1e;
            --bg-secondary: #2d2d30;
            --text-primary: #cccccc;
            --text-secondary: #9e9e9e;
            --accent-color: #007acc;
            --success-color: #4caf50;
            --error-color: #f44336;
            --border-color: #464647;
        }
        
        * { margin: 0; padding: 0; box-sizing: border-box; }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            line-height: 1.6;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .header {
            background: var(--bg-secondary);
            padding: 20px;
            border-bottom: 1px solid var(--border-color);
            text-align: center;
        }
        
        .title {
            font-size: 24px;
            color: var(--accent-color);
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: var(--text-secondary);
            font-size: 14px;
        }
        
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            font-size: 12px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--error-color);
            transition: all 0.3s ease;
        }
        
        .status-indicator.connected {
            background: var(--success-color);
            box-shadow: 0 0 8px rgba(76, 175, 80, 0.5);
        }
        
        .main-container {
            flex: 1;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            width: 100%;
        }
        
        .feedback-section {
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .section-title {
            color: var(--accent-color);
            margin-bottom: 16px;
            font-size: 18px;
        }
        
        .input-group {
            margin-bottom: 16px;
        }
        
        .input-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }
        
        .text-input {
            width: 100%;
            background: var(--bg-primary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            padding: 12px;
            color: var(--text-primary);
            font-size: 14px;
            resize: vertical;
            min-height: 100px;
        }
        
        .text-input:focus {
            outline: none;
            border-color: var(--accent-color);
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: var(--accent-color);
            color: white;
        }
        
        .btn-primary:hover {
            background: #005a9e;
        }
        
        .btn-primary:disabled {
            background: #666;
            cursor: not-allowed;
        }
        
        .waiting-message {
            text-align: center;
            padding: 40px;
            color: var(--text-secondary);
        }
        
        .error-message {
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid var(--error-color);
            color: var(--error-color);
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1 class="title">MCP Feedback Enhanced</h1>
        <p class="subtitle">Cloudflare Workers版 - 全球访问</p>
    </div>
    
    <div class="connection-status">
        <div class="status-indicator" id="statusIndicator"></div>
        <span id="statusText">连接中...</span>
    </div>
    
    <div class="main-container">
        <div id="waitingSection" class="waiting-message">
            <h3>等待MCP会话...</h3>
            <p>请在AI助手中调用MCP工具以建立连接</p>
        </div>
        
        <div id="feedbackSection" class="feedback-section" style="display: none;">
            <h3 class="section-title">AI工作摘要</h3>
            <div id="aiSummary" style="margin-bottom: 20px; padding: 12px; background: var(--bg-primary); border-radius: 6px;"></div>
            
            <h3 class="section-title">用户反馈</h3>
            <div id="errorMessage" class="error-message" style="display: none;"></div>
            
            <div class="input-group">
                <label class="input-label" for="feedbackInput">请输入您的反馈：</label>
                <textarea id="feedbackInput" class="text-input" placeholder="请输入您的反馈内容..."></textarea>
            </div>
            
            <button id="submitBtn" class="btn btn-primary">提交反馈</button>
        </div>
    </div>
    
    <script>
        class WorkersMCPClient {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.currentSession = null;
                this.init();
            }
            
            init() {
                this.connectWebSocket();
                this.setupEventListeners();
            }
            
            connectWebSocket() {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = protocol + '//' + window.location.host + '/ws';
                
                console.log('连接WebSocket:', wsUrl);
                this.updateStatus('connecting', '连接中...');
                
                try {
                    this.ws = new WebSocket(wsUrl);
                    this.setupWebSocketEvents();
                } catch (error) {
                    console.error('WebSocket连接失败:', error);
                    this.updateStatus('error', '连接失败');
                }
            }
            
            setupWebSocketEvents() {
                this.ws.onopen = () => {
                    console.log('WebSocket连接已建立');
                    this.isConnected = true;
                    this.updateStatus('connected', '已连接');
                };
                
                this.ws.onmessage = (event) => {
                    try {
                        const data = JSON.parse(event.data);
                        this.handleMessage(data);
                    } catch (error) {
                        console.error('解析消息失败:', error);
                    }
                };
                
                this.ws.onclose = (event) => {
                    console.log('WebSocket连接已关闭');
                    this.isConnected = false;
                    this.updateStatus('disconnected', '连接断开');
                    
                    // 自动重连
                    setTimeout(() => this.connectWebSocket(), 3000);
                };
                
                this.ws.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    this.updateStatus('error', '连接错误');
                };
            }
            
            handleMessage(data) {
                console.log('收到消息:', data);
                
                switch (data.type) {
                    case 'session_created':
                        this.handleSessionCreated(data);
                        break;
                    case 'feedback_submitted':
                        this.handleFeedbackSubmitted(data);
                        break;
                    case 'error':
                        this.showError(data.message);
                        break;
                }
            }
            
            handleSessionCreated(data) {
                this.currentSession = data.session;
                document.getElementById('waitingSection').style.display = 'none';
                document.getElementById('feedbackSection').style.display = 'block';
                document.getElementById('aiSummary').innerHTML = this.formatSummary(data.session.summary);
            }
            
            handleFeedbackSubmitted(data) {
                document.getElementById('feedbackInput').value = '';
                document.getElementById('submitBtn').disabled = false;
                document.getElementById('submitBtn').textContent = '提交反馈';
                this.showSuccess('反馈已提交成功！');
            }
            
            formatSummary(summary) {
                // 简单的Markdown渲染
                return summary
                    .replace(/\\n/g, '<br>')
                    .replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')
                    .replace(/\\*(.*?)\\*/g, '<em>$1</em>');
            }
            
            submitFeedback() {
                const feedback = document.getElementById('feedbackInput').value.trim();
                
                if (!feedback) {
                    this.showError('请输入反馈内容');
                    return;
                }
                
                if (!this.isConnected) {
                    this.showError('WebSocket未连接');
                    return;
                }
                
                document.getElementById('submitBtn').disabled = true;
                document.getElementById('submitBtn').textContent = '提交中...';
                
                this.ws.send(JSON.stringify({
                    type: 'submit_feedback',
                    feedback: feedback,
                    session_id: this.currentSession?.id
                }));
            }
            
            updateStatus(status, text) {
                const indicator = document.getElementById('statusIndicator');
                const statusText = document.getElementById('statusText');
                
                indicator.className = 'status-indicator ' + status;
                statusText.textContent = text;
            }
            
            showError(message) {
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                setTimeout(() => errorDiv.style.display = 'none', 5000);
            }
            
            showSuccess(message) {
                // 简单的成功提示
                const errorDiv = document.getElementById('errorMessage');
                errorDiv.style.background = 'rgba(76, 175, 80, 0.1)';
                errorDiv.style.borderColor = 'var(--success-color)';
                errorDiv.style.color = 'var(--success-color)';
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
                setTimeout(() => errorDiv.style.display = 'none', 3000);
            }
            
            setupEventListeners() {
                document.getElementById('submitBtn').addEventListener('click', () => {
                    this.submitFeedback();
                });
                
                document.getElementById('feedbackInput').addEventListener('keydown', (e) => {
                    if (e.ctrlKey && e.key === 'Enter') {
                        this.submitFeedback();
                    }
                });
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new WorkersMCPClient();
        });
    </script>
</body>
</html>
`;

export default {
    async fetch(request, env, ctx) {
        const url = new URL(request.url);
        
        // 处理WebSocket升级请求
        if (request.headers.get('Upgrade') === 'websocket') {
            return handleWebSocket(request, env);
        }
        
        // 处理HTTP请求
        if (url.pathname === '/') {
            return new Response(HTML_TEMPLATE, {
                headers: { 'Content-Type': 'text/html' }
            });
        }
        
        // 健康检查
        if (url.pathname === '/health') {
            return new Response(JSON.stringify({ status: 'ok', timestamp: Date.now() }), {
                headers: { 'Content-Type': 'application/json' }
            });
        }
        
        return new Response('Not Found', { status: 404 });
    }
};

async function handleWebSocket(request, env) {
    const webSocketPair = new WebSocketPair();
    const [client, server] = Object.values(webSocketPair);

    // 获取本地MCP服务器地址
    const localMcpUrl = env.LOCAL_MCP_URL;
    console.log('LOCAL_MCP_URL:', localMcpUrl);

    if (!localMcpUrl) {
        console.error('未配置本地MCP服务器地址');
        server.close(1011, '未配置本地MCP服务器地址');
        return new Response(null, { status: 101, webSocket: client });
    }

    // 先接受客户端连接
    server.accept();

    // 连接到本地MCP服务器
    try {
        console.log('尝试连接到:', localMcpUrl + '/ws');
        const mcpWs = new WebSocket(localMcpUrl + '/ws');

        // 等待MCP WebSocket连接建立
        mcpWs.addEventListener('open', () => {
            console.log('MCP WebSocket连接已建立');
            // 发送连接成功消息给客户端
            server.send(JSON.stringify({
                type: 'connection_status',
                status: 'connected',
                message: '已连接到本地MCP服务器'
            }));
        });

        mcpWs.addEventListener('error', (error) => {
            console.error('MCP WebSocket错误:', error);
            server.send(JSON.stringify({
                type: 'connection_status',
                status: 'error',
                message: '连接本地MCP服务器失败'
            }));
        });

        // 设置消息转发
        server.addEventListener('message', (event) => {
            console.log('收到客户端消息:', event.data);
            if (mcpWs.readyState === WebSocket.OPEN) {
                mcpWs.send(event.data);
            } else {
                console.warn('MCP WebSocket未就绪，状态:', mcpWs.readyState);
            }
        });

        mcpWs.addEventListener('message', (event) => {
            console.log('收到MCP服务器消息:', event.data);
            if (server.readyState === WebSocket.OPEN) {
                server.send(event.data);
            }
        });

        // 处理连接关闭
        server.addEventListener('close', () => {
            console.log('客户端WebSocket连接关闭');
            if (mcpWs.readyState === WebSocket.OPEN) {
                mcpWs.close();
            }
        });

        mcpWs.addEventListener('close', (event) => {
            console.log('MCP WebSocket连接关闭:', event.code, event.reason);
            if (server.readyState === WebSocket.OPEN) {
                server.close(event.code, event.reason);
            }
        });

    } catch (error) {
        console.error('连接本地MCP服务器失败:', error);
        server.send(JSON.stringify({
            type: 'connection_status',
            status: 'error',
            message: '无法连接到本地MCP服务器: ' + error.message
        }));
    }

    return new Response(null, { status: 101, webSocket: client });
}
