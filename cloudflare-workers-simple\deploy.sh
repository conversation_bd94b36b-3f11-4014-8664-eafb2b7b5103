#!/bin/bash

# MCP Feedback Enhanced - 简化版一键部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v wrangler &> /dev/null; then
        log_error "wrangler未安装，请先安装："
        echo "npm install -g wrangler"
        exit 1
    fi
    
    if ! command -v ngrok &> /dev/null; then
        log_warning "ngrok未安装，请从 https://ngrok.com/ 下载安装"
        echo "或使用包管理器安装："
        echo "  macOS: brew install ngrok"
        echo "  Windows: choco install ngrok"
        echo "  Linux: snap install ngrok"
    fi
    
    log_success "依赖检查完成"
}

# 检查本地MCP服务器
check_local_server() {
    log_info "检查本地MCP服务器..."
    
    if curl -s http://localhost:8765/health > /dev/null; then
        log_success "本地MCP服务器运行正常"
    else
        log_warning "本地MCP服务器未运行"
        echo "请先启动MCP服务器："
        echo "  uvx mcp-feedback-enhanced@latest test --web"
        
        read -p "是否继续部署？(y/N): " continue_deploy
        if [[ ! $continue_deploy =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# 设置ngrok
setup_ngrok() {
    log_info "设置ngrok..."
    
    if command -v ngrok &> /dev/null; then
        log_info "检测到ngrok，请在新终端运行："
        echo "  ngrok http 8765"
        echo ""
        read -p "请输入ngrok提供的HTTPS URL (如: https://abc123.ngrok.io): " NGROK_URL
        
        if [[ -z "$NGROK_URL" ]]; then
            log_error "ngrok URL不能为空"
            exit 1
        fi
        
        # 转换为WebSocket URL
        WEBSOCKET_URL="${NGROK_URL/https:/wss:}"
        log_success "WebSocket URL: $WEBSOCKET_URL"
    else
        log_warning "ngrok未安装，请手动配置本地服务器暴露"
        read -p "请输入本地服务器的WebSocket URL: " WEBSOCKET_URL
    fi
}

# 登录Cloudflare
login_cloudflare() {
    log_info "检查Cloudflare登录状态..."
    
    if ! wrangler whoami &> /dev/null; then
        log_warning "需要登录Cloudflare账户"
        wrangler login
    fi
    
    log_success "Cloudflare登录验证完成"
}

# 配置环境变量
configure_environment() {
    log_info "配置环境变量..."
    
    echo "$WEBSOCKET_URL" | wrangler secret put LOCAL_MCP_URL
    
    log_success "环境变量配置完成"
}

# 部署Workers
deploy_workers() {
    log_info "部署Cloudflare Workers..."
    
    if wrangler deploy; then
        log_success "Workers部署成功"
        
        # 获取部署URL
        WORKER_URL=$(wrangler subdomain get 2>/dev/null | grep -o 'https://.*workers.dev' || echo "")
        
        if [[ -n "$WORKER_URL" ]]; then
            log_success "访问URL: $WORKER_URL"
        else
            log_info "请在Cloudflare Dashboard中查看Workers URL"
        fi
    else
        log_error "Workers部署失败"
        exit 1
    fi
}

# 测试部署
test_deployment() {
    log_info "测试部署..."
    
    if [[ -n "$WORKER_URL" ]]; then
        if curl -s "$WORKER_URL/health" > /dev/null; then
            log_success "部署测试通过"
        else
            log_warning "部署测试失败，请检查配置"
        fi
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 部署完成！"
    echo ""
    log_info "部署信息："
    log_info "- Workers URL: ${WORKER_URL:-请在Cloudflare Dashboard查看}"
    log_info "- 本地MCP服务器: http://localhost:8765"
    log_info "- ngrok URL: $NGROK_URL"
    echo ""
    log_info "使用说明："
    log_info "1. 确保本地MCP服务器运行: uvx mcp-feedback-enhanced@latest test --web"
    log_info "2. 确保ngrok运行: ngrok http 8765"
    log_info "3. 访问Workers URL使用应用"
    echo ""
    log_info "管理命令："
    log_info "- 查看日志: wrangler tail"
    log_info "- 更新配置: wrangler secret put LOCAL_MCP_URL"
    log_info "- 删除Workers: wrangler delete"
}

# 主函数
main() {
    log_info "开始MCP Feedback Enhanced简化版部署..."
    
    check_dependencies
    check_local_server
    setup_ngrok
    login_cloudflare
    configure_environment
    deploy_workers
    test_deployment
    show_deployment_info
}

# 错误处理
trap 'log_error "部署过程中发生错误"' ERR

# 执行主函数
main "$@"
