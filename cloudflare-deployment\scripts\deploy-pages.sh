#!/bin/bash

# Cloudflare Pages部署脚本
# 用于将静态资源部署到Cloudflare Pages

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
CLOUDFLARE_DIR="$(dirname "$SCRIPT_DIR")"
PAGES_DIR="$CLOUDFLARE_DIR/pages"
PROJECT_ROOT="$(dirname "$CLOUDFLARE_DIR")"

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查wrangler
    if ! command -v wrangler &> /dev/null; then
        log_error "wrangler未安装，请先安装Cloudflare CLI"
        echo "安装命令: npm install -g wrangler"
        exit 1
    fi
    
    # 检查Python
    if ! command -v python3 &> /dev/null; then
        log_error "Python 3未安装"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 构建静态资源
build_static_files() {
    log_info "构建静态资源..."
    
    cd "$SCRIPT_DIR"
    
    if python3 build-static.py --clean; then
        log_success "静态资源构建完成"
    else
        log_error "静态资源构建失败"
        exit 1
    fi
}

# 验证构建结果
validate_build() {
    log_info "验证构建结果..."
    
    # 检查必要文件
    local required_files=(
        "$PAGES_DIR/index.html"
        "$PAGES_DIR/cloudflare-config.js"
        "$PAGES_DIR/_redirects"
        "$PAGES_DIR/_headers"
        "$PAGES_DIR/package.json"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -f "$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    # 检查静态资源目录
    if [ ! -d "$PAGES_DIR/static" ]; then
        log_error "静态资源目录不存在: $PAGES_DIR/static"
        exit 1
    fi
    
    log_success "构建结果验证通过"
}

# 登录Cloudflare
login_cloudflare() {
    log_info "检查Cloudflare登录状态..."
    
    if ! wrangler whoami &> /dev/null; then
        log_warning "需要登录Cloudflare账户"
        wrangler login
    fi
    
    log_success "Cloudflare登录验证完成"
}

# 创建或更新Pages项目
deploy_to_pages() {
    log_info "部署到Cloudflare Pages..."
    
    cd "$PAGES_DIR"
    
    # 获取项目名称
    read -p "请输入Cloudflare Pages项目名称 (默认: mcp-feedback-enhanced): " PROJECT_NAME
    PROJECT_NAME=${PROJECT_NAME:-mcp-feedback-enhanced}
    
    log_info "项目名称: $PROJECT_NAME"
    
    # 检查项目是否存在
    if wrangler pages project list | grep -q "$PROJECT_NAME"; then
        log_info "项目已存在，执行更新部署..."
        
        # 部署到现有项目
        if wrangler pages deploy . --project-name="$PROJECT_NAME"; then
            log_success "部署完成"
        else
            log_error "部署失败"
            exit 1
        fi
    else
        log_info "创建新项目并部署..."
        
        # 创建新项目并部署
        if wrangler pages deploy . --project-name="$PROJECT_NAME" --compatibility-date="2024-01-01"; then
            log_success "项目创建并部署完成"
        else
            log_error "项目创建失败"
            exit 1
        fi
    fi
    
    # 获取部署URL
    local deployment_url=$(wrangler pages deployment list --project-name="$PROJECT_NAME" --format=json | jq -r '.[0].url' 2>/dev/null || echo "")
    
    if [ -n "$deployment_url" ]; then
        log_success "部署URL: $deployment_url"
    fi
}

# 配置自定义域名
configure_custom_domain() {
    log_info "配置自定义域名..."
    
    read -p "是否要配置自定义域名? (y/N): " configure_domain
    
    if [[ $configure_domain =~ ^[Yy]$ ]]; then
        read -p "请输入您的域名 (例如: feedback.example.com): " CUSTOM_DOMAIN
        
        if [ -n "$CUSTOM_DOMAIN" ]; then
            log_info "配置域名: $CUSTOM_DOMAIN"
            
            if wrangler pages domain add "$CUSTOM_DOMAIN" --project-name="$PROJECT_NAME"; then
                log_success "自定义域名配置完成"
                log_info "请确保您的域名DNS指向Cloudflare"
            else
                log_warning "自定义域名配置失败，请手动在Cloudflare Dashboard中配置"
            fi
        fi
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "🎉 Cloudflare Pages部署完成！"
    echo
    log_info "部署信息:"
    log_info "- 项目名称: $PROJECT_NAME"
    log_info "- Pages目录: $PAGES_DIR"
    
    if [ -n "$deployment_url" ]; then
        log_info "- 部署URL: $deployment_url"
    fi
    
    if [ -n "$CUSTOM_DOMAIN" ]; then
        log_info "- 自定义域名: https://$CUSTOM_DOMAIN"
    fi
    
    echo
    log_info "下一步:"
    log_info "1. 启动本地MCP服务器"
    log_info "2. 配置并启动Cloudflare Tunnel"
    log_info "3. 访问部署的URL测试功能"
    echo
    log_info "相关命令:"
    log_info "- 查看项目: wrangler pages project list"
    log_info "- 查看部署: wrangler pages deployment list --project-name=$PROJECT_NAME"
    log_info "- 删除项目: wrangler pages project delete $PROJECT_NAME"
}

# 主函数
main() {
    log_info "开始Cloudflare Pages部署..."
    
    check_dependencies
    build_static_files
    validate_build
    login_cloudflare
    deploy_to_pages
    configure_custom_domain
    show_deployment_info
}

# 错误处理
trap 'log_error "部署过程中发生错误，请检查上述输出"' ERR

# 执行主函数
main "$@"
